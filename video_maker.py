#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
采访对话视频制作工具
根据音频文件和PDF字幕制作视频
"""

import os
import sys
import subprocess
import json
from pathlib import Path
import re
from moviepy.editor import *
from moviepy.video.tools.subtitles import SubtitlesClip
import pdfplumber

def check_dependencies():
    """检查必要的依赖"""
    required_packages = [
        'moviepy',
        'PyPDF2',
        'pdfplumber',
        'numpy'
    ]

    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_').lower())
            print(f"✓ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} 未安装")

    if missing_packages:
        print(f"\n需要安装以下包: {', '.join(missing_packages)}")
        install_cmd = f"pip install {' '.join(missing_packages)}"
        print(f"运行命令: {install_cmd}")
        return False
    return True

def extract_pdf_text(pdf_path):
    """从PDF文件中提取文本"""
    try:
        import pdfplumber
        
        text_content = []
        with pdfplumber.open(pdf_path) as pdf:
            for page in pdf.pages:
                text = page.extract_text()
                if text:
                    text_content.append(text)
        
        full_text = '\n'.join(text_content)
        print(f"成功从PDF提取文本，共 {len(full_text)} 字符")
        return full_text
        
    except ImportError:
        print("pdfplumber未安装，尝试使用PyPDF2...")
        try:
            import PyPDF2
            
            text_content = []
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text = page.extract_text()
                    if text:
                        text_content.append(text)
            
            full_text = '\n'.join(text_content)
            print(f"成功从PDF提取文本，共 {len(full_text)} 字符")
            return full_text
            
        except ImportError:
            print("错误：需要安装 pdfplumber 或 PyPDF2")
            return None
    except Exception as e:
        print(f"提取PDF文本时出错: {e}")
        return None

def parse_dialogue_text(text):
    """解析对话文本，分离问题和答案"""
    dialogues = []

    lines = text.split('\n')
    current_question = ""
    current_answer = ""

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # 检测问题模式 Q1:, Q2:, etc.
        if re.match(r'^Q\d+:', line):
            # 如果有之前的对话，先保存
            if current_question and current_answer:
                dialogues.append({
                    'question': current_question.strip(),
                    'answer': current_answer.strip()
                })
            # 开始新的问题
            current_question = line[line.find(':')+1:].strip()
            current_answer = ""
        # 检测答案模式 A1:, A2:, etc.
        elif re.match(r'^A\d+:', line):
            current_answer = line[line.find(':')+1:].strip()
        else:
            # 继续添加到当前的问题或答案
            if current_answer:
                current_answer += ' ' + line
            elif current_question:
                current_question += ' ' + line

    # 添加最后一个对话
    if current_question and current_answer:
        dialogues.append({
            'question': current_question.strip(),
            'answer': current_answer.strip()
        })

    print(f"解析出 {len(dialogues)} 段对话")
    for i, dialogue in enumerate(dialogues, 1):
        print(f"Q{i}: {dialogue['question'][:50]}...")
        print(f"A{i}: {dialogue['answer'][:50]}...")
        print()

    return dialogues

def get_audio_files():
    """获取音频文件列表"""
    current_dir = Path('.')
    
    question_files = []
    answer_files = []
    
    # 查找question文件
    for i in range(1, 11):
        # 尝试不同的文件名格式
        possible_names = [f'question{i}.m4a', f'question {i}.m4a']
        for name in possible_names:
            if (current_dir / name).exists():
                question_files.append(str(current_dir / name))
                break
    
    # 查找answer文件
    for i in range(1, 11):
        answer_file = current_dir / f'answer{i}.m4a'
        if answer_file.exists():
            answer_files.append(str(answer_file))
    
    print(f"找到 {len(question_files)} 个问题音频文件")
    print(f"找到 {len(answer_files)} 个答案音频文件")
    
    return question_files, answer_files

def check_ffmpeg():
    """检查FFmpeg是否可用"""
    try:
        result = subprocess.run(['ffmpeg', '-version'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ FFmpeg 可用")
            return True
        else:
            print("✗ FFmpeg 不可用")
            return False
    except FileNotFoundError:
        print("✗ FFmpeg 未安装")
        print("请安装FFmpeg: https://ffmpeg.org/download.html")
        return False

def combine_audio_files(question_files, answer_files, output_path="combined_audio.wav"):
    """合并问题和答案音频文件"""
    print("\n开始合并音频文件...")

    audio_clips = []

    # 确保文件数量匹配
    min_count = min(len(question_files), len(answer_files))

    for i in range(min_count):
        print(f"处理第 {i+1} 组对话...")

        # 加载问题音频
        try:
            question_audio = AudioFileClip(question_files[i])
            print(f"  问题音频: {question_files[i]} (时长: {question_audio.duration:.2f}秒)")
            audio_clips.append(question_audio)
        except Exception as e:
            print(f"  错误：无法加载问题音频 {question_files[i]}: {e}")
            continue

        # 加载答案音频
        try:
            answer_audio = AudioFileClip(answer_files[i])
            print(f"  答案音频: {answer_files[i]} (时长: {answer_audio.duration:.2f}秒)")
            audio_clips.append(answer_audio)
        except Exception as e:
            print(f"  错误：无法加载答案音频 {answer_files[i]}: {e}")
            continue

        # 在每组对话之间添加短暂停顿
        if i < min_count - 1:
            silence = AudioClip(lambda t: [0], duration=1.0)  # 1秒静音
            audio_clips.append(silence)

    if not audio_clips:
        print("错误：没有成功加载任何音频文件")
        return None

    # 合并所有音频
    print("正在合并音频...")
    final_audio = concatenate_audioclips(audio_clips)

    # 保存合并后的音频
    print(f"保存音频到: {output_path}")
    final_audio.write_audiofile(output_path)

    print(f"音频合并完成！总时长: {final_audio.duration:.2f}秒")

    # 清理内存
    for clip in audio_clips:
        if hasattr(clip, 'close'):
            clip.close()

    return final_audio

def create_subtitle_file(dialogues, audio_duration, output_path="subtitles.srt"):
    """创建字幕文件"""
    print("\n创建字幕文件...")

    # 估算每段对话的时间
    total_dialogues = len(dialogues)
    if total_dialogues == 0:
        print("错误：没有对话内容")
        return None

    # 简单的时间分配：平均分配时间
    time_per_dialogue = audio_duration / total_dialogues

    subtitle_content = []
    current_time = 0

    for i, dialogue in enumerate(dialogues, 1):
        # 问题字幕
        start_time = current_time
        end_time = current_time + time_per_dialogue / 2

        subtitle_content.append(f"{i*2-1}")
        subtitle_content.append(f"{format_time(start_time)} --> {format_time(end_time)}")
        subtitle_content.append(f"Q{i}: {dialogue['question']}")
        subtitle_content.append("")

        # 答案字幕
        start_time = end_time
        end_time = current_time + time_per_dialogue

        subtitle_content.append(f"{i*2}")
        subtitle_content.append(f"{format_time(start_time)} --> {format_time(end_time)}")
        subtitle_content.append(f"A{i}: {dialogue['answer']}")
        subtitle_content.append("")

        current_time = end_time

    # 保存字幕文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(subtitle_content))

    print(f"字幕文件已保存到: {output_path}")
    return output_path

def format_time(seconds):
    """将秒数转换为SRT时间格式"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    millisecs = int((seconds % 1) * 1000)
    return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"

def create_video(audio_path, subtitle_path, output_path="interview_video.mp4"):
    """创建带字幕的视频"""
    print("\n开始创建视频...")

    # 加载音频
    audio = AudioFileClip(audio_path)
    duration = audio.duration

    # 创建简单的背景（纯色背景）
    background = ColorClip(size=(1280, 720), color=(30, 30, 30), duration=duration)

    # 添加标题文本
    title_text = TextClip("Interview Dialogue",
                         fontsize=50,
                         color='white',
                         font='Arial-Bold')
    title_text = title_text.set_position(('center', 100)).set_duration(duration)

    # 创建字幕
    def subtitle_generator(txt):
        return TextClip(txt,
                       fontsize=24,
                       color='white',
                       font='Arial',
                       size=(1200, None),
                       method='caption')

    # 读取字幕文件并创建字幕剪辑
    subtitles = SubtitlesClip(subtitle_path, subtitle_generator)
    subtitles = subtitles.set_position(('center', 'bottom'))

    # 合成视频
    video = CompositeVideoClip([background, title_text, subtitles])
    video = video.set_audio(audio)

    # 输出视频
    print(f"正在渲染视频到: {output_path}")
    video.write_videofile(output_path,
                         fps=24,
                         codec='libx264',
                         audio_codec='aac')

    print("视频创建完成！")

    # 清理内存
    audio.close()
    video.close()

    return output_path

def main():
    """主函数"""
    print("=== 采访对话视频制作工具 ===\n")
    
    # 检查依赖
    print("1. 检查Python依赖...")
    if not check_dependencies():
        print("请先安装缺失的Python包")
        return
    
    print("\n2. 检查FFmpeg...")
    if not check_ffmpeg():
        print("请先安装FFmpeg")
        return
    
    print("\n3. 检查文件...")
    # 检查PDF文件
    pdf_path = Path('采访对话.pdf')
    if not pdf_path.exists():
        print("错误：找不到 '采访对话.pdf' 文件")
        return
    
    # 检查音频文件
    question_files, answer_files = get_audio_files()
    if len(question_files) == 0 or len(answer_files) == 0:
        print("错误：找不到足够的音频文件")
        return
    
    print("\n4. 提取PDF文本...")
    pdf_text = extract_pdf_text(pdf_path)
    if not pdf_text:
        print("错误：无法提取PDF文本")
        return
    
    # 保存提取的文本到文件以便检查
    with open('extracted_text.txt', 'w', encoding='utf-8') as f:
        f.write(pdf_text)
    print("已将提取的文本保存到 'extracted_text.txt'")
    
    print("\n5. 解析对话文本...")
    dialogues = parse_dialogue_text(pdf_text)

    if len(dialogues) == 0:
        print("错误：没有解析到对话内容")
        return

    print("\n6. 合并音频文件...")
    combined_audio = combine_audio_files(question_files, answer_files)
    if not combined_audio:
        print("错误：音频合并失败")
        return

    print("\n7. 创建字幕文件...")
    subtitle_file = create_subtitle_file(dialogues, combined_audio.duration)
    if not subtitle_file:
        print("错误：字幕创建失败")
        return

    print("\n8. 创建视频...")
    try:
        video_file = create_video("combined_audio.wav", subtitle_file)
        print(f"\n=== 视频制作完成！ ===")
        print(f"输出文件: {video_file}")
        print("您可以使用任何视频播放器播放这个文件。")
    except Exception as e:
        print(f"视频创建失败: {e}")
        print("您可以手动使用音频文件和字幕文件制作视频")

    # 清理内存
    if combined_audio:
        combined_audio.close()

if __name__ == "__main__":
    main()
