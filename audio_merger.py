#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频文件合并工具
使用pydub库合并音频文件
"""

from pathlib import Path
import subprocess
import sys

def check_pydub():
    """检查pydub是否可用"""
    try:
        import pydub
        print("✓ pydub 可用")
        return True
    except ImportError:
        print("✗ pydub 未安装")
        print("正在安装 pydub...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pydub"])
            import pydub
            print("✓ pydub 安装成功")
            return True
        except Exception as e:
            print(f"✗ pydub 安装失败: {e}")
            return False

def get_audio_files():
    """获取音频文件列表"""
    current_dir = Path('.')
    
    question_files = []
    answer_files = []
    
    # 查找question文件
    for i in range(1, 11):
        # 尝试不同的文件名格式
        possible_names = [f'question{i}.m4a', f'question {i}.m4a']
        for name in possible_names:
            if (current_dir / name).exists():
                question_files.append(str(current_dir / name))
                break
    
    # 查找answer文件
    for i in range(1, 11):
        answer_file = current_dir / f'answer{i}.m4a'
        if answer_file.exists():
            answer_files.append(str(answer_file))
    
    print(f"找到 {len(question_files)} 个问题音频文件")
    print(f"找到 {len(answer_files)} 个答案音频文件")
    
    return question_files, answer_files

def merge_audio_with_pydub(question_files, answer_files, output_file="combined_audio.wav"):
    """使用pydub合并音频文件"""
    try:
        from pydub import AudioSegment
        from pydub.silence import make_silence
    except ImportError:
        print("错误：无法导入pydub")
        return False
    
    print("\n开始合并音频文件...")
    
    # 创建空的音频段
    combined = AudioSegment.empty()
    
    # 创建0.5秒的静音
    silence = make_silence(duration=500)  # 500毫秒
    
    min_count = min(len(question_files), len(answer_files))
    
    for i in range(min_count):
        print(f"处理第 {i+1} 组对话...")
        
        try:
            # 加载问题音频
            question_audio = AudioSegment.from_file(question_files[i])
            print(f"  问题音频: {question_files[i]} (时长: {len(question_audio)/1000:.2f}秒)")
            combined += question_audio
            
            # 加载答案音频
            answer_audio = AudioSegment.from_file(answer_files[i])
            print(f"  答案音频: {answer_files[i]} (时长: {len(answer_audio)/1000:.2f}秒)")
            combined += answer_audio
            
            # 在每组对话之间添加静音（除了最后一组）
            if i < min_count - 1:
                combined += silence
                
        except Exception as e:
            print(f"  错误：处理第 {i+1} 组音频时出错: {e}")
            continue
    
    if len(combined) == 0:
        print("错误：没有成功合并任何音频")
        return False
    
    # 导出合并后的音频
    print(f"\n保存合并后的音频到: {output_file}")
    combined.export(output_file, format="wav")
    
    print(f"音频合并完成！")
    print(f"总时长: {len(combined)/1000:.2f}秒")
    print(f"文件大小: {Path(output_file).stat().st_size / (1024*1024):.2f} MB")
    
    return True

def create_playlist_file(question_files, answer_files, output_file="playlist.txt"):
    """创建播放列表文件，用于其他工具"""
    print(f"\n创建播放列表文件: {output_file}")
    
    playlist_content = []
    min_count = min(len(question_files), len(answer_files))
    
    for i in range(min_count):
        playlist_content.append(f"# 第 {i+1} 组对话")
        playlist_content.append(question_files[i])
        playlist_content.append(answer_files[i])
        playlist_content.append("")  # 空行分隔
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(playlist_content))
    
    print(f"播放列表已保存到: {output_file}")

def main():
    """主函数"""
    print("=== 音频文件合并工具 ===\n")
    
    # 检查音频文件
    print("1. 检查音频文件...")
    question_files, answer_files = get_audio_files()
    
    if len(question_files) == 0 or len(answer_files) == 0:
        print("错误：找不到足够的音频文件")
        return
    
    # 创建播放列表
    print("\n2. 创建播放列表...")
    create_playlist_file(question_files, answer_files)
    
    # 检查pydub
    print("\n3. 检查pydub...")
    if not check_pydub():
        print("\n无法使用pydub合并音频。")
        print("您可以使用以下方法手动合并音频：")
        print("1. 使用Audacity等音频编辑软件")
        print("2. 使用在线音频合并工具")
        print("3. 参考生成的 playlist.txt 文件了解音频顺序")
        return
    
    # 合并音频
    print("\n4. 合并音频...")
    success = merge_audio_with_pydub(question_files, answer_files)
    
    if success:
        print("\n=== 音频合并完成！ ===")
        print("生成的文件：")
        print("- combined_audio.wav (合并后的音频)")
        print("- playlist.txt (播放列表)")
        print("- subtitles.srt (字幕文件)")
        print("\n您现在可以使用这些文件制作视频了！")
    else:
        print("\n音频合并失败。请检查错误信息。")

if __name__ == "__main__":
    main()
