#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用MoviePy制作采访对话视频
"""

from pathlib import Path
import re

try:
    from moviepy.editor import AudioFileClip, AudioClip, concatenate_audioclips
    from moviepy.editor import ColorClip, TextClip, CompositeVideoClip
    MOVIEPY_AVAILABLE = True
except ImportError:
    MOVIEPY_AVAILABLE = False

def parse_dialogue_text(text):
    """解析对话文本，分离问题和答案"""
    dialogues = []
    
    lines = text.split('\n')
    current_question = ""
    current_answer = ""
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # 检测问题模式 Q1:, Q2:, etc.
        if re.match(r'^Q\d+:', line):
            # 如果有之前的对话，先保存
            if current_question and current_answer:
                dialogues.append({
                    'question': current_question.strip(),
                    'answer': current_answer.strip()
                })
            # 开始新的问题
            current_question = line[line.find(':')+1:].strip()
            current_answer = ""
        # 检测答案模式 A1:, A2:, etc.
        elif re.match(r'^A\d+:', line):
            current_answer = line[line.find(':')+1:].strip()
        else:
            # 继续添加到当前的问题或答案
            if current_answer:
                current_answer += ' ' + line
            elif current_question:
                current_question += ' ' + line
    
    # 添加最后一个对话
    if current_question and current_answer:
        dialogues.append({
            'question': current_question.strip(),
            'answer': current_answer.strip()
        })
    
    print(f"解析出 {len(dialogues)} 段对话")
    return dialogues

def get_audio_files():
    """获取音频文件列表"""
    current_dir = Path('.')
    
    question_files = []
    answer_files = []
    
    # 查找question文件
    for i in range(1, 11):
        # 尝试不同的文件名格式
        possible_names = [f'question{i}.m4a', f'question {i}.m4a']
        for name in possible_names:
            if (current_dir / name).exists():
                question_files.append(str(current_dir / name))
                break
    
    # 查找answer文件
    for i in range(1, 11):
        answer_file = current_dir / f'answer{i}.m4a'
        if answer_file.exists():
            answer_files.append(str(answer_file))
    
    print(f"找到 {len(question_files)} 个问题音频文件")
    print(f"找到 {len(answer_files)} 个答案音频文件")
    
    return question_files, answer_files

def create_video_with_moviepy():
    """使用MoviePy创建视频"""
    if not MOVIEPY_AVAILABLE:
        print("MoviePy不可用，请安装moviepy包")
        return False

    print("MoviePy导入成功")
    
    print("\n开始使用MoviePy制作视频...")
    
    # 读取文本
    text_file = Path('extracted_text.txt')
    if not text_file.exists():
        print("错误：找不到 'extracted_text.txt' 文件")
        return False
    
    with open(text_file, 'r', encoding='utf-8') as f:
        pdf_text = f.read()
    
    # 解析对话
    dialogues = parse_dialogue_text(pdf_text)
    if len(dialogues) == 0:
        print("错误：没有解析到对话内容")
        return False
    
    # 获取音频文件
    question_files, answer_files = get_audio_files()
    if len(question_files) == 0 or len(answer_files) == 0:
        print("错误：找不到足够的音频文件")
        return False
    
    # 合并音频
    print("正在合并音频...")
    audio_clips = []
    
    min_count = min(len(question_files), len(answer_files), len(dialogues))
    
    for i in range(min_count):
        print(f"处理第 {i+1} 组对话...")
        
        try:
            # 加载问题音频
            question_audio = AudioFileClip(question_files[i])
            audio_clips.append(question_audio)
            
            # 加载答案音频
            answer_audio = AudioFileClip(answer_files[i])
            audio_clips.append(answer_audio)
            
            # 添加短暂停顿（除了最后一组）
            if i < min_count - 1:
                silence = AudioClip(lambda t: 0, duration=0.5)
                audio_clips.append(silence)
                
        except Exception as e:
            print(f"处理第 {i+1} 组音频时出错: {e}")
            continue
    
    if not audio_clips:
        print("错误：没有成功加载任何音频")
        return False
    
    # 合并音频
    final_audio = concatenate_audioclips(audio_clips)
    total_duration = final_audio.duration
    print(f"音频合并完成，总时长: {total_duration:.2f}秒")
    
    # 创建视频背景
    print("创建视频背景...")
    background = ColorClip(size=(1280, 720), color=(20, 20, 40), duration=total_duration)
    
    # 添加标题
    title = TextClip("Interview Dialogue - Study Abroad", 
                    fontsize=40, 
                    color='white',
                    font='Arial-Bold' if Path('C:/Windows/Fonts/arialbd.ttf').exists() else None)
    title = title.set_position(('center', 50)).set_duration(total_duration)
    
    # 创建字幕
    print("创建字幕...")
    subtitle_clips = []
    current_time = 0
    
    for i, dialogue in enumerate(dialogues[:min_count]):
        # 估算每个问答的时间
        question_duration = 5  # 假设问题5秒
        answer_duration = 10   # 假设答案10秒
        
        # 问题字幕
        q_text = f"Q{i+1}: {dialogue['question']}"
        if len(q_text) > 80:
            q_text = q_text[:77] + "..."
        
        question_subtitle = TextClip(q_text,
                                   fontsize=24,
                                   color='yellow',
                                   font='Arial' if Path('C:/Windows/Fonts/arial.ttf').exists() else None,
                                   size=(1200, None),
                                   method='caption')
        question_subtitle = question_subtitle.set_position(('center', 400)).set_start(current_time).set_duration(question_duration)
        subtitle_clips.append(question_subtitle)
        
        current_time += question_duration
        
        # 答案字幕
        a_text = f"A{i+1}: {dialogue['answer']}"
        if len(a_text) > 120:
            a_text = a_text[:117] + "..."
        
        answer_subtitle = TextClip(a_text,
                                 fontsize=22,
                                 color='white',
                                 font='Arial' if Path('C:/Windows/Fonts/arial.ttf').exists() else None,
                                 size=(1200, None),
                                 method='caption')
        answer_subtitle = answer_subtitle.set_position(('center', 500)).set_start(current_time).set_duration(answer_duration)
        subtitle_clips.append(answer_subtitle)
        
        current_time += answer_duration + 0.5  # 加上停顿时间
    
    # 合成视频
    print("合成视频...")
    video_clips = [background, title] + subtitle_clips
    final_video = CompositeVideoClip(video_clips)
    final_video = final_video.set_audio(final_audio)
    
    # 输出视频
    output_path = "interview_dialogue.mp4"
    print(f"正在渲染视频到: {output_path}")
    print("这可能需要几分钟时间，请耐心等待...")
    
    final_video.write_videofile(output_path, 
                               fps=24,
                               codec='libx264',
                               audio_codec='aac',
                               temp_audiofile='temp-audio.m4a',
                               remove_temp=True)
    
    print(f"\n=== 视频制作完成！ ===")
    print(f"输出文件: {output_path}")
    
    # 清理内存
    final_audio.close()
    final_video.close()
    for clip in audio_clips:
        if hasattr(clip, 'close'):
            clip.close()
    
    return True

def main():
    """主函数"""
    print("=== MoviePy采访对话视频制作工具 ===\n")
    
    success = create_video_with_moviepy()
    
    if success:
        print("\n视频制作成功！您可以使用任何视频播放器播放生成的视频文件。")
    else:
        print("\n视频制作失败。请检查错误信息并重试。")

if __name__ == "__main__":
    main()
