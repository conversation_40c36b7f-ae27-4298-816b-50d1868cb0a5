# 采访对话视频制作项目

## 项目概述

本项目旨在根据您提供的音频文件和PDF字幕文档，制作一个包含字幕的采访对话视频。

## 已完成的工作

### ✅ 1. PDF文本提取
- **输入文件**: `采访对话.pdf`
- **输出文件**: `extracted_text.txt`
- **内容**: 10组问答对话的完整文本

### ✅ 2. 音频文件分析
- **问题音频**: 10个文件 (question1.m4a 到 question10.m4a)
- **答案音频**: 10个文件 (answer1.m4a 到 answer10.m4a)
- **播放顺序**: 已生成 `playlist.txt` 文件

### ✅ 3. 字幕文件生成
- **输出文件**: `subtitles.srt`
- **格式**: 标准SRT字幕格式
- **内容**: 包含所有10组问答的时间轴和文本

## 生成的文件

| 文件名 | 描述 | 用途 |
|--------|------|------|
| `extracted_text.txt` | 从PDF提取的文本 | 查看对话内容 |
| `subtitles.srt` | 字幕文件 | 视频制作时添加字幕 |
| `playlist.txt` | 音频播放顺序 | 了解音频文件顺序 |
| `create_video_instructions.md` | 详细制作指南 | 视频制作步骤 |

## 对话内容预览

```
Q1: How did you decide to study abroad?
A1: Well, I've always been really curious about other cultures, and I felt like studying abroad would be an amazing way to experience something totally different...

Q2: What kind of materials did you have to prepare for your application?
A2: Oh, there was a lot to get ready! I needed transcripts, my English test scores, letters of recommendation...

... (共10组问答)
```

## 制作视频的推荐方法

### 🎯 方法1：在线工具（最简单）
1. **Kapwing** (kapwing.com)
   - 上传音频文件（按playlist.txt顺序）
   - 上传字幕文件 `subtitles.srt`
   - 选择背景和样式

2. **剪映网页版**
   - 导入所有音频文件
   - 添加字幕文本
   - 设置背景

### 🎯 方法2：手机APP
1. **剪映APP**
   - 按顺序导入音频
   - 复制粘贴字幕文本
   - 添加背景

2. **CapCut**
   - 类似剪映的操作流程

### 🎯 方法3：桌面软件
1. **DaVinci Resolve**（免费专业软件）
2. **OpenShot**（免费开源）
3. **Adobe Premiere Pro**（付费专业）

## 音频文件顺序

按以下顺序合并音频文件：

```
1. question1.m4a → answer1.m4a
2. question2.m4a → answer2.m4a
3. question 3.m4a → answer3.m4a  ⚠️ 注意空格
4. question4.m4a → answer4.m4a
5. question5.m4a → answer5.m4a
6. question6.m4a → answer6.m4a
7. question7.m4a → answer7.m4a
8. question8.m4a → answer8.m4a
9. question9.m4a → answer9.m4a
10. question10.m4a → answer10.m4a
```

## 建议的视频设置

- **分辨率**: 1280x720 (HD) 或 1920x1080 (Full HD)
- **帧率**: 24fps 或 30fps
- **背景色**: 深蓝色 (#1a1a2e) 或深灰色 (#2c2c2c)
- **字幕颜色**: 白色或黄色
- **字幕位置**: 屏幕下方居中
- **标题**: "Interview Dialogue - Study Abroad"

## 字幕时间安排

当前字幕文件假设每个问答对话30秒：
- 问题：15秒
- 答案：15秒

您可以根据实际音频长度调整字幕时间。

## 技术说明

### 使用的工具和库
- **Python 3.10+**
- **pdfplumber**: PDF文本提取
- **moviepy**: 视频处理（可选）
- **pydub**: 音频处理（需要ffmpeg）

### 遇到的技术限制
- FFmpeg未安装，无法直接合并音频
- MoviePy版本兼容性问题
- 建议使用现有的视频编辑工具

## 下一步操作

1. **选择制作方法**：从上述推荐方法中选择一种
2. **准备素材**：
   - 音频文件（按playlist.txt顺序）
   - 字幕文件（subtitles.srt）
   - 背景图片或颜色
3. **制作视频**：按照选择的方法进行制作
4. **导出视频**：建议格式MP4，编码H.264

## 联系支持

如果您在制作过程中遇到任何问题，可以：
1. 查看 `create_video_instructions.md` 获取详细指导
2. 使用生成的字幕文件和播放列表
3. 选择最适合您技术水平的制作方法

---

**项目状态**: ✅ 准备工作完成，可以开始制作视频

**预计制作时间**: 30-60分钟（取决于选择的方法和熟练程度）
