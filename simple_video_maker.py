#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的采访对话视频制作工具
"""

from pathlib import Path
import re

def parse_dialogue_text(text):
    """解析对话文本，分离问题和答案"""
    dialogues = []
    
    lines = text.split('\n')
    current_question = ""
    current_answer = ""
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # 检测问题模式 Q1:, Q2:, etc.
        if re.match(r'^Q\d+:', line):
            # 如果有之前的对话，先保存
            if current_question and current_answer:
                dialogues.append({
                    'question': current_question.strip(),
                    'answer': current_answer.strip()
                })
            # 开始新的问题
            current_question = line[line.find(':')+1:].strip()
            current_answer = ""
        # 检测答案模式 A1:, A2:, etc.
        elif re.match(r'^A\d+:', line):
            current_answer = line[line.find(':')+1:].strip()
        else:
            # 继续添加到当前的问题或答案
            if current_answer:
                current_answer += ' ' + line
            elif current_question:
                current_question += ' ' + line
    
    # 添加最后一个对话
    if current_question and current_answer:
        dialogues.append({
            'question': current_question.strip(),
            'answer': current_answer.strip()
        })
    
    print(f"解析出 {len(dialogues)} 段对话")
    return dialogues

def get_audio_files():
    """获取音频文件列表"""
    current_dir = Path('.')
    
    question_files = []
    answer_files = []
    
    # 查找question文件
    for i in range(1, 11):
        # 尝试不同的文件名格式
        possible_names = [f'question{i}.m4a', f'question {i}.m4a']
        for name in possible_names:
            if (current_dir / name).exists():
                question_files.append(str(current_dir / name))
                break
    
    # 查找answer文件
    for i in range(1, 11):
        answer_file = current_dir / f'answer{i}.m4a'
        if answer_file.exists():
            answer_files.append(str(answer_file))
    
    print(f"找到 {len(question_files)} 个问题音频文件")
    print(f"找到 {len(answer_files)} 个答案音频文件")
    
    return question_files, answer_files

def create_subtitle_file(dialogues, output_path="subtitles.srt"):
    """创建字幕文件"""
    print("\n创建字幕文件...")
    
    if len(dialogues) == 0:
        print("错误：没有对话内容")
        return None
    
    # 简单的时间分配：每个问答对话30秒
    time_per_dialogue = 30.0
    
    subtitle_content = []
    current_time = 0
    
    for i, dialogue in enumerate(dialogues, 1):
        # 问题字幕
        start_time = current_time
        end_time = current_time + time_per_dialogue / 2
        
        subtitle_content.append(f"{i*2-1}")
        subtitle_content.append(f"{format_time(start_time)} --> {format_time(end_time)}")
        subtitle_content.append(f"Q{i}: {dialogue['question']}")
        subtitle_content.append("")
        
        # 答案字幕
        start_time = end_time
        end_time = current_time + time_per_dialogue
        
        subtitle_content.append(f"{i*2}")
        subtitle_content.append(f"{format_time(start_time)} --> {format_time(end_time)}")
        subtitle_content.append(f"A{i}: {dialogue['answer']}")
        subtitle_content.append("")
        
        current_time = end_time
    
    # 保存字幕文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(subtitle_content))
    
    print(f"字幕文件已保存到: {output_path}")
    return output_path

def format_time(seconds):
    """将秒数转换为SRT时间格式"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    millisecs = int((seconds % 1) * 1000)
    return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"

def create_ffmpeg_command(question_files, answer_files, subtitle_file, output_video="interview_video.mp4"):
    """创建FFmpeg命令来合并音频和添加字幕"""
    print("\n生成FFmpeg命令...")
    
    # 创建音频合并的输入列表
    input_files = []
    filter_complex = []
    
    # 添加所有音频文件
    for i in range(min(len(question_files), len(answer_files))):
        input_files.extend(['-i', question_files[i], '-i', answer_files[i]])
    
    # 创建音频合并的filter
    audio_inputs = []
    for i in range(len(input_files) // 2):
        audio_inputs.append(f"[{i*2}][{i*2+1}]")
    
    # 合并音频的filter
    concat_filter = f"{''.join(audio_inputs)}concat=n={len(audio_inputs)*2}:v=0:a=1[audio]"
    filter_complex.append(concat_filter)
    
    # 创建视频背景
    video_filter = f"color=c=black:s=1280x720:d={len(question_files)*30}[bg]"
    filter_complex.append(video_filter)
    
    # 完整的FFmpeg命令
    cmd = [
        'ffmpeg',
        '-y',  # 覆盖输出文件
    ] + input_files + [
        '-filter_complex', ';'.join(filter_complex),
        '-map', '[bg]',
        '-map', '[audio]',
        '-vf', f"subtitles={subtitle_file}",
        '-c:v', 'libx264',
        '-c:a', 'aac',
        '-shortest',
        output_video
    ]
    
    return cmd

def main():
    """主函数"""
    print("=== 简化的采访对话视频制作工具 ===\n")
    
    # 检查文件
    print("1. 检查文件...")
    
    # 检查PDF文本文件
    text_file = Path('extracted_text.txt')
    if not text_file.exists():
        print("错误：找不到 'extracted_text.txt' 文件")
        print("请先运行 extract_pdf.py 提取PDF文本")
        return
    
    # 读取文本
    with open(text_file, 'r', encoding='utf-8') as f:
        pdf_text = f.read()
    
    # 检查音频文件
    question_files, answer_files = get_audio_files()
    if len(question_files) == 0 or len(answer_files) == 0:
        print("错误：找不到足够的音频文件")
        return
    
    print("\n2. 解析对话文本...")
    dialogues = parse_dialogue_text(pdf_text)
    
    if len(dialogues) == 0:
        print("错误：没有解析到对话内容")
        return
    
    print("\n3. 创建字幕文件...")
    subtitle_file = create_subtitle_file(dialogues)
    if not subtitle_file:
        print("错误：字幕创建失败")
        return
    
    print("\n4. 生成FFmpeg命令...")
    ffmpeg_cmd = create_ffmpeg_command(question_files, answer_files, subtitle_file)
    
    print("\n=== 准备完成 ===")
    print("FFmpeg命令已生成。您可以运行以下命令来创建视频：")
    print()
    print(' '.join(f'"{arg}"' if ' ' in arg else arg for arg in ffmpeg_cmd))
    print()
    print("或者运行以下Python命令自动执行：")
    print("import subprocess")
    print(f"subprocess.run({ffmpeg_cmd})")

if __name__ == "__main__":
    main()
