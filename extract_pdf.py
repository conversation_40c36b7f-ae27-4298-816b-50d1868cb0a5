#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的PDF文本提取工具
"""

import pdfplumber
from pathlib import Path

def extract_pdf_text(pdf_path):
    """从PDF文件中提取文本"""
    try:
        text_content = []
        print(f"正在打开PDF文件: {pdf_path}")
        
        with pdfplumber.open(pdf_path) as pdf:
            print(f"PDF有 {len(pdf.pages)} 页")
            
            for i, page in enumerate(pdf.pages):
                print(f"正在处理第 {i+1} 页...")
                text = page.extract_text()
                if text:
                    text_content.append(f"=== 第 {i+1} 页 ===\n{text}\n")
                else:
                    print(f"第 {i+1} 页没有文本内容")
        
        full_text = '\n'.join(text_content)
        print(f"成功提取文本，共 {len(full_text)} 字符")
        return full_text
        
    except Exception as e:
        print(f"提取PDF文本时出错: {e}")
        return None

def main():
    pdf_path = Path('采访对话.pdf')
    
    if not pdf_path.exists():
        print(f"错误：找不到文件 {pdf_path}")
        return
    
    print("开始提取PDF文本...")
    text = extract_pdf_text(pdf_path)
    
    if text:
        # 保存到文件
        output_path = 'extracted_text.txt'
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(text)
        print(f"文本已保存到: {output_path}")
        
        # 显示前500个字符作为预览
        print("\n=== 文本预览 ===")
        print(text[:500])
        if len(text) > 500:
            print("...")
    else:
        print("提取失败")

if __name__ == "__main__":
    main()
